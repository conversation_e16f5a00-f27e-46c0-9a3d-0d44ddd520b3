import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import os
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('seaborn-v0_8-whitegrid')

# 苹果风格配色方案
APPLE_COLORS = {
    'primary': '#007AFF',      # 苹果蓝
    'secondary': '#5856D6',    # 紫色
    'success': '#34C759',      # 绿色
    'warning': '#FF9500',      # 橙色
    'danger': '#FF3B30',       # 红色
    'gray': '#8E8E93',         # 灰色
    'light_gray': '#F2F2F7',   # 浅灰色
    'dark': '#1C1C1E'          # 深色
}

# 读取数据
data_dir = "数据可视化数据集-A"

# 读取选定的三个数据集
house_df = pd.read_excel(os.path.join(data_dir, "二手房数据.xlsx"))
restaurant_df = pd.read_excel(os.path.join(data_dir, "某餐厅顾客消费记录.xlsx"))
marketing_df = pd.read_excel(os.path.join(data_dir, "营销和产品销售表.xlsx"))

# 创建输出目录
os.makedirs('charts', exist_ok=True)

print("开始生成可视化图表...")

# ==================== 二手房数据可视化 ====================

# 图表1：散点图 - 面积与总价的关系
def create_house_scatter():
    plt.figure(figsize=(12, 8))
    
    # 按区域分组，使用不同颜色
    districts = house_df['所在区'].unique()
    colors = [APPLE_COLORS['primary'], APPLE_COLORS['secondary'], APPLE_COLORS['success'], 
              APPLE_COLORS['warning'], APPLE_COLORS['danger']]
    
    for i, district in enumerate(districts[:5]):  # 只显示前5个区域避免过于拥挤
        district_data = house_df[house_df['所在区'] == district]
        plt.scatter(district_data['面积（平方米）'], district_data['总价（万元）'], 
                   alpha=0.6, s=50, color=colors[i % len(colors)], label=district)
    
    plt.xlabel('面积（平方米）', fontsize=14, fontweight='bold')
    plt.ylabel('总价（万元）', fontsize=14, fontweight='bold')
    plt.title('北京二手房面积与总价关系分析', fontsize=16, fontweight='bold', pad=20)
    plt.legend(title='所在区', title_fontsize=12, fontsize=10)
    plt.grid(True, alpha=0.3)
    
    # 添加趋势线
    z = np.polyfit(house_df['面积（平方米）'], house_df['总价（万元）'], 1)
    p = np.poly1d(z)
    plt.plot(house_df['面积（平方米）'], p(house_df['面积（平方米）']), 
             color=APPLE_COLORS['dark'], linestyle='--', alpha=0.8, linewidth=2)
    
    plt.tight_layout()
    plt.savefig('charts/1_二手房面积价格散点图.png', dpi=300, bbox_inches='tight')
    plt.show()

# 图表2：箱形图 - 各区域房价分布
def create_house_boxplot():
    plt.figure(figsize=(14, 8))
    
    # 选择房源数量较多的前8个区域
    top_districts = house_df['所在区'].value_counts().head(8).index
    filtered_data = house_df[house_df['所在区'].isin(top_districts)]
    
    box_plot = plt.boxplot([filtered_data[filtered_data['所在区'] == district]['单价（元/平方米）'] 
                           for district in top_districts],
                          labels=top_districts, patch_artist=True)
    
    # 设置箱形图颜色
    colors = [APPLE_COLORS['primary'], APPLE_COLORS['secondary'], APPLE_COLORS['success'], 
              APPLE_COLORS['warning']] * 2
    for patch, color in zip(box_plot['boxes'], colors):
        patch.set_facecolor(color)
        patch.set_alpha(0.7)
    
    plt.xlabel('行政区', fontsize=14, fontweight='bold')
    plt.ylabel('单价（元/平方米）', fontsize=14, fontweight='bold')
    plt.title('北京各区二手房单价分布对比', fontsize=16, fontweight='bold', pad=20)
    plt.xticks(rotation=45)
    plt.grid(True, alpha=0.3, axis='y')
    
    plt.tight_layout()
    plt.savefig('charts/2_各区房价箱形图.png', dpi=300, bbox_inches='tight')
    plt.show()

# 图表3：直方图 - 单价分布
def create_house_histogram():
    plt.figure(figsize=(12, 8))
    
    # 创建直方图
    n, bins, patches = plt.hist(house_df['单价（元/平方米）'], bins=30, alpha=0.7, 
                               color=APPLE_COLORS['primary'], edgecolor='white', linewidth=1)
    
    # 为不同价格区间设置不同颜色
    for i, patch in enumerate(patches):
        if bins[i] < 50000:
            patch.set_facecolor(APPLE_COLORS['success'])
        elif bins[i] < 80000:
            patch.set_facecolor(APPLE_COLORS['warning'])
        else:
            patch.set_facecolor(APPLE_COLORS['danger'])
    
    plt.xlabel('单价（元/平方米）', fontsize=14, fontweight='bold')
    plt.ylabel('房源数量', fontsize=14, fontweight='bold')
    plt.title('北京二手房单价分布直方图', fontsize=16, fontweight='bold', pad=20)
    
    # 添加统计信息
    mean_price = house_df['单价（元/平方米）'].mean()
    median_price = house_df['单价（元/平方米）'].median()
    plt.axvline(mean_price, color=APPLE_COLORS['dark'], linestyle='--', 
                label=f'平均价格: {mean_price:,.0f}元/㎡')
    plt.axvline(median_price, color=APPLE_COLORS['secondary'], linestyle='--', 
                label=f'中位价格: {median_price:,.0f}元/㎡')
    
    plt.legend(fontsize=12)
    plt.grid(True, alpha=0.3, axis='y')
    plt.tight_layout()
    plt.savefig('charts/3_房价分布直方图.png', dpi=300, bbox_inches='tight')
    plt.show()

# ==================== 餐厅消费数据可视化 ====================

# 图表4：条形图 - 各分店平均消费金额
def create_restaurant_bar():
    plt.figure(figsize=(10, 8))

    # 计算各分店平均消费金额
    avg_consumption = restaurant_df.groupby('分店')['消费金额（元）'].mean().sort_values(ascending=False)

    bars = plt.bar(avg_consumption.index, avg_consumption.values,
                   color=[APPLE_COLORS['primary'], APPLE_COLORS['secondary'], APPLE_COLORS['success']])

    # 在柱子上添加数值标签
    for bar in bars:
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + 5,
                f'{height:.1f}元', ha='center', va='bottom', fontsize=12, fontweight='bold')

    plt.xlabel('分店', fontsize=14, fontweight='bold')
    plt.ylabel('平均消费金额（元）', fontsize=14, fontweight='bold')
    plt.title('各分店平均消费金额对比', fontsize=16, fontweight='bold', pad=20)
    plt.grid(True, alpha=0.3, axis='y')

    plt.tight_layout()
    plt.savefig('charts/4_分店消费条形图.png', dpi=300, bbox_inches='tight')
    plt.show()

# 图表5：小提琴图 - 不同顾客类型的消费分布
def create_restaurant_violin():
    plt.figure(figsize=(10, 8))

    # 创建小提琴图
    parts = plt.violinplot([restaurant_df[restaurant_df['顾客类型'] == '会员']['消费金额（元）'],
                           restaurant_df[restaurant_df['顾客类型'] == '普通顾客']['消费金额（元）']],
                          positions=[1, 2], showmeans=True, showmedians=True)

    # 设置颜色
    colors = [APPLE_COLORS['primary'], APPLE_COLORS['secondary']]
    for pc, color in zip(parts['bodies'], colors):
        pc.set_facecolor(color)
        pc.set_alpha(0.7)

    plt.xticks([1, 2], ['会员', '普通顾客'])
    plt.xlabel('顾客类型', fontsize=14, fontweight='bold')
    plt.ylabel('消费金额（元）', fontsize=14, fontweight='bold')
    plt.title('不同顾客类型消费金额分布', fontsize=16, fontweight='bold', pad=20)
    plt.grid(True, alpha=0.3, axis='y')

    plt.tight_layout()
    plt.savefig('charts/5_顾客类型小提琴图.png', dpi=300, bbox_inches='tight')
    plt.show()

# 图表6：饼图 - 性别消费占比
def create_restaurant_pie():
    plt.figure(figsize=(10, 8))

    # 计算性别消费总额
    gender_consumption = restaurant_df.groupby('性别')['消费金额（元）'].sum()

    colors = [APPLE_COLORS['primary'], APPLE_COLORS['secondary']]
    wedges, texts, autotexts = plt.pie(gender_consumption.values,
                                      labels=gender_consumption.index,
                                      autopct='%1.1f%%',
                                      colors=colors,
                                      startangle=90,
                                      explode=(0.05, 0.05))

    # 设置文字样式
    for autotext in autotexts:
        autotext.set_color('white')
        autotext.set_fontsize(14)
        autotext.set_fontweight('bold')

    for text in texts:
        text.set_fontsize(14)
        text.set_fontweight('bold')

    plt.title('不同性别顾客消费金额占比', fontsize=16, fontweight='bold', pad=20)

    # 添加图例
    plt.legend(wedges, [f'{label}: {value:,.0f}元' for label, value in zip(gender_consumption.index, gender_consumption.values)],
              title="消费总额", loc="center left", bbox_to_anchor=(1, 0, 0.5, 1))

    plt.tight_layout()
    plt.savefig('charts/6_性别消费饼图.png', dpi=300, bbox_inches='tight')
    plt.show()

# ==================== 营销销售数据可视化 ====================

# 图表7：折线图 - 营销费用与订单金额趋势
def create_marketing_line():
    fig, ax1 = plt.subplots(figsize=(14, 8))

    # 营销费用折线
    ax1.plot(marketing_df['日期'], marketing_df['营销费用（元）'],
             color=APPLE_COLORS['primary'], linewidth=3, marker='o', markersize=6, label='营销费用')
    ax1.set_xlabel('日期', fontsize=14, fontweight='bold')
    ax1.set_ylabel('营销费用（元）', color=APPLE_COLORS['primary'], fontsize=14, fontweight='bold')
    ax1.tick_params(axis='y', labelcolor=APPLE_COLORS['primary'])

    # 创建第二个y轴
    ax2 = ax1.twinx()
    ax2.plot(marketing_df['日期'], marketing_df['订单金额（元）'],
             color=APPLE_COLORS['success'], linewidth=3, marker='s', markersize=6, label='订单金额')
    ax2.set_ylabel('订单金额（元）', color=APPLE_COLORS['success'], fontsize=14, fontweight='bold')
    ax2.tick_params(axis='y', labelcolor=APPLE_COLORS['success'])

    plt.title('营销费用与订单金额趋势分析', fontsize=16, fontweight='bold', pad=20)

    # 设置x轴日期格式
    plt.xticks(rotation=45)

    # 添加图例
    lines1, labels1 = ax1.get_legend_handles_labels()
    lines2, labels2 = ax2.get_legend_handles_labels()
    ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper left')

    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('charts/7_营销趋势折线图.png', dpi=300, bbox_inches='tight')
    plt.show()

# 图表8：面积图 - 展现量和点击量趋势
def create_marketing_area():
    plt.figure(figsize=(14, 8))

    # 创建面积图
    plt.fill_between(marketing_df['日期'], marketing_df['展现量'],
                     alpha=0.7, color=APPLE_COLORS['primary'], label='展现量')
    plt.fill_between(marketing_df['日期'], marketing_df['点击量'],
                     alpha=0.7, color=APPLE_COLORS['secondary'], label='点击量')

    plt.xlabel('日期', fontsize=14, fontweight='bold')
    plt.ylabel('数量', fontsize=14, fontweight='bold')
    plt.title('展现量与点击量趋势面积图', fontsize=16, fontweight='bold', pad=20)
    plt.legend(fontsize=12)
    plt.xticks(rotation=45)
    plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('charts/8_展现点击面积图.png', dpi=300, bbox_inches='tight')
    plt.show()

# 图表9：密度图 - 点击率分布
def create_marketing_density():
    plt.figure(figsize=(12, 8))

    # 计算点击率
    marketing_df['点击率'] = marketing_df['点击量'] / marketing_df['展现量'] * 100

    # 创建密度图
    from scipy import stats
    density = stats.gaussian_kde(marketing_df['点击率'])
    xs = np.linspace(marketing_df['点击率'].min(), marketing_df['点击率'].max(), 200)

    plt.fill_between(xs, density(xs), alpha=0.7, color=APPLE_COLORS['primary'])
    plt.plot(xs, density(xs), color=APPLE_COLORS['dark'], linewidth=2)

    # 添加统计线
    mean_rate = marketing_df['点击率'].mean()
    median_rate = marketing_df['点击率'].median()
    plt.axvline(mean_rate, color=APPLE_COLORS['danger'], linestyle='--',
                label=f'平均点击率: {mean_rate:.2f}%', linewidth=2)
    plt.axvline(median_rate, color=APPLE_COLORS['warning'], linestyle='--',
                label=f'中位点击率: {median_rate:.2f}%', linewidth=2)

    plt.xlabel('点击率 (%)', fontsize=14, fontweight='bold')
    plt.ylabel('密度', fontsize=14, fontweight='bold')
    plt.title('点击率分布密度图', fontsize=16, fontweight='bold', pad=20)
    plt.legend(fontsize=12)
    plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('charts/9_点击率密度图.png', dpi=300, bbox_inches='tight')
    plt.show()

# 执行营销数据可视化
create_marketing_line()
create_marketing_area()
create_marketing_density()

print("营销销售数据可视化完成！")

# 执行所有可视化
print("\n=== 开始生成所有可视化图表 ===")

# 执行二手房数据可视化
print("1. 生成二手房数据图表...")
create_house_scatter()
create_house_boxplot()
create_house_histogram()

# 执行餐厅数据可视化
print("2. 生成餐厅消费数据图表...")
create_restaurant_bar()
create_restaurant_violin()
create_restaurant_pie()

# 执行营销数据可视化
print("3. 生成营销销售数据图表...")
create_marketing_line()
create_marketing_area()
create_marketing_density()

print("\n🎉 所有9个可视化图表生成完成！")
print("图表保存在 'charts' 文件夹中")
