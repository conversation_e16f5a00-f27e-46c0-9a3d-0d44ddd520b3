import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from matplotlib import rcParams
import platform
import os

# 深度修复中文字体显示问题
def setup_chinese_font():
    """深度设置中文字体，确保在所有系统上都能正确显示中文"""
    print("正在配置中文字体...")
    
    # 清除matplotlib字体缓存
    try:
        import matplotlib
        matplotlib.font_manager._rebuild()
    except:
        pass
    
    # 获取系统信息
    system = platform.system()
    print(f"检测到系统: {system}")
    
    # 定义候选字体列表（按优先级排序）
    if system == "Windows":
        chinese_fonts = ['Microsoft YaHei', 'Microsoft YaHei UI', 'SimHei', 'SimSun', 'KaiTi', 'FangSong']
    elif system == "Darwin":  # macOS
        chinese_fonts = ['PingFang SC', 'Hiragino Sans GB', 'STHeiti', 'Arial Unicode MS']
    else:  # Linux
        chinese_fonts = ['WenQuanYi Micro Hei', 'WenQuanYi Zen Hei', 'Noto Sans CJK SC', 'Source Han Sans CN', 'DejaVu Sans']
    
    # 获取系统可用字体
    available_fonts = [f.name for f in fm.fontManager.ttflist]
    print(f"系统可用字体数量: {len(available_fonts)}")
    
    # 查找可用的中文字体
    selected_font = None
    for font in chinese_fonts:
        if font in available_fonts:
            selected_font = font
            print(f"✅ 找到可用中文字体: {font}")
            break
    
    if not selected_font:
        selected_font = 'DejaVu Sans'  # 最后的备选方案
        print(f"⚠️  使用备选字体: {selected_font}")
    
    # 设置matplotlib参数
    rcParams['font.sans-serif'] = [selected_font] + chinese_fonts + ['DejaVu Sans', 'Arial']
    rcParams['axes.unicode_minus'] = False
    rcParams['font.size'] = 12
    
    print(f"✅ 中文字体配置完成，使用字体: {selected_font}")
    return selected_font

# 执行字体设置
CHINESE_FONT = setup_chinese_font()

# 苹果风格配色方案
APPLE_COLORS = {
    'primary': '#007AFF',      # 苹果蓝
    'secondary': '#5856D6',    # 紫色
    'success': '#34C759',      # 绿色
    'warning': '#FF9500',      # 橙色
    'danger': '#FF3B30',       # 红色
    'gray': '#8E8E93',         # 灰色
    'light_gray': '#F2F2F7',   # 浅灰色
    'dark': '#1C1C1E'          # 深色
}

# 设置全局字体属性
FONT_PROPS = {'family': CHINESE_FONT, 'size': 12, 'weight': 'normal'}
TITLE_PROPS = {'family': CHINESE_FONT, 'size': 16, 'weight': 'bold'}
LABEL_PROPS = {'family': CHINESE_FONT, 'size': 14, 'weight': 'bold'}
LEGEND_PROPS = {'family': CHINESE_FONT, 'size': 10, 'weight': 'normal'}

def create_test_chart():
    """创建一个测试图表验证中文显示"""
    print("开始生成测试图表...")
    
    # 读取二手房数据
    try:
        house_df = pd.read_excel(os.path.join("数据可视化数据集-A", "二手房数据.xlsx"))
        print(f"成功读取数据，共{len(house_df)}条记录")
    except Exception as e:
        print(f"读取数据失败: {e}")
        return
    
    # 创建散点图
    plt.figure(figsize=(12, 8))
    
    # 按区域分组，使用不同颜色
    districts = house_df['所在区'].unique()[:5]  # 只显示前5个区域
    colors = [APPLE_COLORS['primary'], APPLE_COLORS['secondary'], APPLE_COLORS['success'], 
              APPLE_COLORS['warning'], APPLE_COLORS['danger']]
    
    for i, district in enumerate(districts):
        district_data = house_df[house_df['所在区'] == district]
        plt.scatter(district_data['面积（平方米）'], district_data['总价（万元）'], 
                   alpha=0.6, s=50, color=colors[i % len(colors)], label=district)
    
    # 设置标签和标题（使用字体属性）
    plt.xlabel('面积（平方米）', fontproperties=LABEL_PROPS)
    plt.ylabel('总价（万元）', fontproperties=LABEL_PROPS)
    plt.title('北京二手房面积与总价关系分析（中文字体测试）', fontproperties=TITLE_PROPS, pad=20)
    
    # 设置图例
    legend = plt.legend(title='所在区', prop=LEGEND_PROPS)
    legend.get_title().set_fontproperties(LEGEND_PROPS)
    
    plt.grid(True, alpha=0.3)
    
    # 添加趋势线
    z = np.polyfit(house_df['面积（平方米）'], house_df['总价（万元）'], 1)
    p = np.poly1d(z)
    plt.plot(house_df['面积（平方米）'], p(house_df['面积（平方米）']), 
             color=APPLE_COLORS['dark'], linestyle='--', alpha=0.8, linewidth=2)
    
    plt.tight_layout()
    
    # 保存图表
    os.makedirs('test_charts', exist_ok=True)
    plt.savefig('test_charts/中文字体测试_散点图.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("✅ 测试图表生成完成！")
    print("请检查 'test_charts/中文字体测试_散点图.png' 文件中的中文是否正确显示")

if __name__ == "__main__":
    create_test_chart()
