import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import platform
from matplotlib import rcParams
import numpy as np

# 深度修复中文字体显示问题
def setup_chinese_font():
    """
    深度设置中文字体，确保在所有系统上都能正确显示中文
    """
    print("正在配置中文字体...")
    
    # 清除matplotlib字体缓存
    try:
        import matplotlib
        matplotlib.font_manager._rebuild()
    except:
        pass
    
    # 获取系统信息
    system = platform.system()
    print(f"检测到系统: {system}")
    
    # 定义候选字体列表（按优先级排序）
    chinese_fonts = []
    
    if system == "Windows":
        chinese_fonts = [
            'Microsoft YaHei',
            'Microsoft YaHei UI', 
            'SimHei',
            'SimSun',
            'KaiTi',
            'FangSong'
        ]
    elif system == "Darwin":  # macOS
        chinese_fonts = [
            'PingFang SC',
            'Hiragino Sans GB',
            'STHeiti',
            'Arial Unicode MS'
        ]
    else:  # Linux
        chinese_fonts = [
            'WenQuanYi Micro Hei',
            'WenQuanYi Zen Hei',
            'Noto Sans CJK SC',
            'Source Han Sans CN',
            'DejaVu Sans'
        ]
    
    # 获取系统可用字体
    available_fonts = [f.name for f in fm.fontManager.ttflist]
    print(f"系统可用字体数量: {len(available_fonts)}")
    
    # 查找可用的中文字体
    selected_font = None
    for font in chinese_fonts:
        if font in available_fonts:
            selected_font = font
            print(f"✅ 找到可用中文字体: {font}")
            break
    
    if not selected_font:
        # 如果没找到预定义字体，尝试查找任何包含中文的字体
        for font_name in available_fonts:
            if any(keyword in font_name.lower() for keyword in ['chinese', 'cjk', 'han', 'hei', 'kai', 'song']):
                selected_font = font_name
                print(f"✅ 找到备用中文字体: {font_name}")
                break
    
    if not selected_font:
        selected_font = 'DejaVu Sans'  # 最后的备选方案
        print(f"⚠️  使用备选字体: {selected_font}")
    
    # 设置matplotlib参数
    rcParams['font.sans-serif'] = [selected_font] + chinese_fonts + ['DejaVu Sans', 'Arial']
    rcParams['axes.unicode_minus'] = False
    rcParams['font.size'] = 12
    
    print(f"✅ 中文字体配置完成，使用字体: {selected_font}")
    return selected_font

# 执行字体设置
CHINESE_FONT = setup_chinese_font()

# 设置全局字体属性
FONT_PROPS = {
    'family': CHINESE_FONT,
    'size': 12,
    'weight': 'normal'
}

TITLE_PROPS = {
    'family': CHINESE_FONT,
    'size': 16,
    'weight': 'bold'
}

LABEL_PROPS = {
    'family': CHINESE_FONT,
    'size': 14,
    'weight': 'bold'
}

# 测试中文字体显示
def test_chinese_display():
    """测试中文字体是否正确显示"""
    print("\n开始测试中文字体显示...")
    
    # 创建测试图表
    plt.figure(figsize=(10, 6))
    
    # 测试数据
    x = np.array([1, 2, 3, 4, 5])
    y = np.array([10, 25, 30, 35, 20])
    labels = ['北京', '上海', '广州', '深圳', '杭州']
    
    # 创建柱状图
    bars = plt.bar(x, y, color=['#007AFF', '#5856D6', '#34C759', '#FF9500', '#FF3B30'])
    
    # 设置中文标签
    plt.xticks(x, labels, fontproperties=FONT_PROPS)
    plt.xlabel('城市', fontproperties=LABEL_PROPS)
    plt.ylabel('数值（万元）', fontproperties=LABEL_PROPS)
    plt.title('中文字体显示测试图表', fontproperties=TITLE_PROPS, pad=20)
    
    # 添加数值标签
    for i, bar in enumerate(bars):
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                f'{height}万元', ha='center', va='bottom', fontproperties=FONT_PROPS)
    
    plt.grid(True, alpha=0.3, axis='y')
    plt.tight_layout()
    
    # 保存测试图表
    plt.savefig('test_chinese_font.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("✅ 中文字体测试完成！")
    print("如果图表中的中文正确显示，说明字体配置成功。")
    print("测试图表已保存为 'test_chinese_font.png'")

if __name__ == "__main__":
    test_chinese_display()
