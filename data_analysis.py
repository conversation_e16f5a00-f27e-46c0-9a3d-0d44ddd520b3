import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os
from pathlib import Path

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 数据集路径
data_dir = "数据可视化数据集-A"

# 读取所有数据集并分析
datasets = {}

# 1. 2022年北京市各行政区常住人口
try:
    df1 = pd.read_excel(os.path.join(data_dir, "2022年北京市各行政区常住人口.xlsx"))
    datasets["北京人口"] = df1
    print("北京市各行政区常住人口数据:")
    print(df1.head())
    print(f"数据形状: {df1.shape}")
    print(f"列名: {df1.columns.tolist()}")
    print("-" * 50)
except Exception as e:
    print(f"读取北京人口数据失败: {e}")

# 2. 地铁站数据
try:
    df2 = pd.read_excel(os.path.join(data_dir, "2022年北京市工作日早高峰出站量前20的地铁站.xlsx"))
    datasets["地铁站"] = df2
    print("北京市地铁站出站量数据:")
    print(df2.head())
    print(f"数据形状: {df2.shape}")
    print(f"列名: {df2.columns.tolist()}")
    print("-" * 50)
except Exception as e:
    print(f"读取地铁站数据失败: {e}")

# 3. 二手房数据
try:
    df3 = pd.read_excel(os.path.join(data_dir, "二手房数据.xlsx"))
    datasets["二手房"] = df3
    print("二手房数据:")
    print(df3.head())
    print(f"数据形状: {df3.shape}")
    print(f"列名: {df3.columns.tolist()}")
    print("-" * 50)
except Exception as e:
    print(f"读取二手房数据失败: {e}")

# 4. 产品销售统计表
try:
    df4 = pd.read_excel(os.path.join(data_dir, "产品销售统计表.xlsx"))
    datasets["产品销售统计"] = df4
    print("产品销售统计数据:")
    print(df4.head())
    print(f"数据形状: {df4.shape}")
    print(f"列名: {df4.columns.tolist()}")
    print("-" * 50)
except Exception as e:
    print(f"读取产品销售统计数据失败: {e}")

# 5. 国内生产总值季度数据
try:
    df5 = pd.read_excel(os.path.join(data_dir, "国内生产总值季度数据.xlsx"))
    datasets["GDP"] = df5
    print("国内生产总值季度数据:")
    print(df5.head())
    print(f"数据形状: {df5.shape}")
    print(f"列名: {df5.columns.tolist()}")
    print("-" * 50)
except Exception as e:
    print(f"读取GDP数据失败: {e}")

# 6. 某公司产品销售数据
try:
    df6 = pd.read_excel(os.path.join(data_dir, "某公司产品销售数据.xlsx"))
    datasets["公司销售"] = df6
    print("某公司产品销售数据:")
    print(df6.head())
    print(f"数据形状: {df6.shape}")
    print(f"列名: {df6.columns.tolist()}")
    print("-" * 50)
except Exception as e:
    print(f"读取公司销售数据失败: {e}")

# 7. 某餐厅顾客消费记录
try:
    df7 = pd.read_excel(os.path.join(data_dir, "某餐厅顾客消费记录.xlsx"))
    datasets["餐厅消费"] = df7
    print("某餐厅顾客消费记录:")
    print(df7.head())
    print(f"数据形状: {df7.shape}")
    print(f"列名: {df7.columns.tolist()}")
    print("-" * 50)
except Exception as e:
    print(f"读取餐厅消费数据失败: {e}")

# 8. 营销和产品销售表
try:
    df8 = pd.read_excel(os.path.join(data_dir, "营销和产品销售表.xlsx"))
    datasets["营销销售"] = df8
    print("营销和产品销售数据:")
    print(df8.head())
    print(f"数据形状: {df8.shape}")
    print(f"列名: {df8.columns.tolist()}")
    print("-" * 50)
except Exception as e:
    print(f"读取营销销售数据失败: {e}")

print(f"\n成功读取的数据集数量: {len(datasets)}")
for name, df in datasets.items():
    print(f"{name}: {df.shape}")

# 选择的三个数据集进行详细分析
selected_datasets = {
    "二手房": datasets["二手房"],
    "餐厅消费": datasets["餐厅消费"],
    "营销销售": datasets["营销销售"]
}

print("\n=== 选定的三个数据集详细信息 ===")
for name, df in selected_datasets.items():
    print(f"\n{name}数据集:")
    print(f"数据形状: {df.shape}")
    print(f"列名: {df.columns.tolist()}")
    print("数据类型:")
    print(df.dtypes)
    print("基本统计信息:")
    print(df.describe())
